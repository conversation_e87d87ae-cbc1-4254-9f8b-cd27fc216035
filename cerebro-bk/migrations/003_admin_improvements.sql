-- migrations/003_admin_improvements.sql

-- AUDIT LOGS
CREATE TABLE IF NOT EXISTS audit_logs (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  user_id BIGINT UNSIGNED NULL,
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50) NULL,
  resource_id VARCHAR(255) NULL,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  details JSON NULL,
  INDEX idx_timestamp (timestamp),
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_resource (resource_type, resource_id),
  CONSTRAINT fk_al_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- OAUTH TOKENS (para tracking de access tokens si necesario)
CREATE TABLE IF NOT EXISTS oauth_tokens (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  token_hash VARCHAR(255) NOT NULL UNIQUE,
  user_id BIGINT UNSIGNED NOT NULL,
  client_id VARCHAR(100) NOT NULL,
  token_type VARCHAR(20) NOT NULL DEFAULT 'access_token',
  scope VARCHAR(500) NULL,
  revoked BOOLEAN NOT NULL DEFAULT FALSE,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_ot_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  CONSTRAINT fk_ot_client FOREIGN KEY (client_id) REFERENCES oidc_clients(client_id) ON DELETE CASCADE,
  INDEX idx_token_hash (token_hash),
  INDEX idx_user_id (user_id),
  INDEX idx_expires (expires_at),
  INDEX idx_revoked (revoked)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ADMIN SESSIONS (para tracking de sesiones admin)
CREATE TABLE IF NOT EXISTS admin_sessions (
  id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  session_id VARCHAR(255) NOT NULL UNIQUE,
  user_id BIGINT UNSIGNED NOT NULL,
  ip_address VARCHAR(45) NULL,
  user_agent TEXT NULL,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_as_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_session_id (session_id),
  INDEX idx_user_id (user_id),
  INDEX idx_expires (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- SYSTEM CONFIG (para configuraciones del sistema)
CREATE TABLE IF NOT EXISTS system_config (
  id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
  config_key VARCHAR(100) NOT NULL UNIQUE,
  config_value TEXT NULL,
  config_type VARCHAR(20) NOT NULL DEFAULT 'string',
  description VARCHAR(255) NULL,
  is_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
  updated_by BIGINT UNSIGNED NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  CONSTRAINT fk_sc_updated_by FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_config_key (config_key),
  INDEX idx_config_type (config_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertar configuraciones por defecto
INSERT IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES
('maintenance_mode', 'false', 'boolean', 'Modo de mantenimiento del sistema'),
('max_login_attempts', '5', 'integer', 'Máximo número de intentos de login'),
('session_timeout', '3600', 'integer', 'Timeout de sesión en segundos'),
('jwk_rotation_days', '90', 'integer', 'Días para rotación automática de JWK');

-- Asegurar que todos los usuarios tengan al menos rol CLIENT
INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT u.id, 2
FROM users u
LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 2
WHERE ur.user_id IS NULL;

-- Asegurar que el admin tenga rol ADMIN
INSERT IGNORE INTO user_roles (user_id, role_id)
SELECT u.id, 1
FROM users u
LEFT JOIN user_roles ur ON ur.user_id = u.id AND ur.role_id = 1
WHERE u.email = '<EMAIL>' AND ur.user_id IS NULL;
