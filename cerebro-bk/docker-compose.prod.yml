# docker-compose.prod.yml - Configuración de producción
version: '3.8'

services:
  # ================================================
  # Aplicación Principal
  # ================================================
  app:
    build:
      context: .
      target: production
    container_name: cerebro-idp-app
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=4000
      - DB_HOST=db
      - DB_PORT=3306
      - DB_USER=cerebro
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_NAME=cerebro
      - REDIS_URL=redis://redis:6379
      - PUBLIC_BASE_URL=${PUBLIC_BASE_URL}
      - OIDC_ISSUER=${OIDC_ISSUER}
      - OIDC_AUDIENCE=${OIDC_AUDIENCE}
      - JWT_SECRET=${JWT_SECRET}
      - ADMIN_PWD=${ADMIN_PWD}
      - CORS_ORIGINS=${CORS_ORIGINS}
      - ACCESS_TOKEN_TTL=${ACCESS_TOKEN_TTL:-3600}
      - REFRESH_TOKEN_TTL=${REFRESH_TOKEN_TTL:-2592000}
      - AUDIT_LOG_RETENTION_DAYS=${AUDIT_LOG_RETENTION_DAYS:-90}
      - LOG_LEVEL=${LOG_LEVEL:-info}
    ports:
      - "4000:4000"
    volumes:
      - app_logs:/app/logs
      - app_backups:/app/backups
    networks:
      - cerebro_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ================================================
  # Base de Datos
  # ================================================
  db:
    image: mariadb:11
    container_name: cerebro-idp-db
    restart: unless-stopped
    environment:
      - MARIADB_ROOT_PASSWORD=${DB_ROOT_PASSWORD}
      - MARIADB_DATABASE=cerebro
      - MARIADB_USER=cerebro
      - MARIADB_PASSWORD=${DB_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./backups:/backups
    networks:
      - cerebro_network
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      interval: 20s
      timeout: 10s
      retries: 5
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=256M
      --max-connections=200
      --slow-query-log=1
      --long-query-time=2

  # ================================================
  # Redis
  # ================================================
  redis:
    image: redis:7-alpine
    container_name: cerebro-idp-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - cerebro_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"
    command: redis-server /usr/local/etc/redis/redis.conf

  # ================================================
  # Nginx Reverse Proxy (Opcional)
  # ================================================
  nginx:
    image: nginx:alpine
    container_name: cerebro-idp-nginx
    restart: unless-stopped
    depends_on:
      - app
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - cerebro_network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

# ================================================
# Volumes
# ================================================
volumes:
  db_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  app_backups:
    driver: local
  nginx_logs:
    driver: local

# ================================================
# Networks
# ================================================
networks:
  cerebro_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
