.
├── conf
│   ├── nginx.conf
│   └── redis.conf
├── docker-compose.dev.yml
├── docker-compose.prod.yml
├── Dockerfile
├── _docs
│   └── file-system.md
├── .env
├── .env.example
├── .gitignore
├── Makefile
├── migrations
│   ├── 001_init.sql
│   ├── 002_seed_minima.sql
│   └── 003_admin_improvements.sql
├── package.json
├── package-lock.json
├── QUICK_START.md
├── README.md
├── scripts
│   ├── cleanup.ts
│   ├── deploy.sh
│   ├── gen-jwk.ts
│   ├── health-check.ts
│   ├── migrate.ts
│   └── seed.ts
├── src
│   ├── crypto
│   │   └── jwk.ts
│   ├── db.ts
│   ├── env.ts
│   ├── index.ts
│   ├── logger.ts
│   ├── middlewares
│   │   ├── auditLog.ts
│   │   ├── errorHandler.ts
│   │   ├── rateLimit.ts
│   │   ├── requireAdmin.ts
│   │   └── requireAuth.ts
│   ├── routes
│   │   ├── adminAudit.ts
│   │   ├── adminAuth.ts
│   │   ├── admin.ts
│   │   ├── authorize.ts
│   │   ├── jwks.ts
│   │   ├── profile.ts
│   │   ├── token.ts
│   │   ├── userinfo.ts
│   │   └── wellKnown.ts
│   └── services
│       ├── api.ts
│       ├── auth.ts
│       ├── oidc.ts
│       ├── tokens.ts
│       └── users.ts
└── tsconfig.json

10 directories, 48 files
