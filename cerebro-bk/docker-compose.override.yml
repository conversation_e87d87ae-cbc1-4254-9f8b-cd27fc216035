# docker-compose.override.yml
# Este archivo se carga automáticamente por Docker Compose
# Proporciona hot reload y mejores herramientas de desarrollo

version: '3.8'

services:
  # ================================================
  # Aplicación con hot reload
  # ================================================
  app:
    build:
      context: .
      target: development
      dockerfile: Dockerfile
    container_name: cerebro-idp-dev
    # Variables adicionales de desarrollo
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
      - DEBUG=cerebro:*
      - NODE_OPTIONS=--inspect=0.0.0.0:9229
    volumes:
      # Hot reload - sincronizar código fuente
      - ./src:/app/src:ro
      - ./scripts:/app/scripts:ro
      - ./migrations:/app/migrations:ro
      - ./package*.json:/app/
      - ./tsconfig.json:/app/tsconfig.json:ro
      - ./.env:/app/.env:ro

      # Mantener node_modules en el contenedor para mejor performance
      - app_node_modules:/app/node_modules

      # Logs persistentes
      - ./logs:/app/logs

      # Cache de TypeScript
      - ts_cache:/app/.ts-cache

    # Override del comando para usar nodemon
    command: npm run dev

    # Ports adicionales para debugging
    ports:
      - "4000:4000"
      - "9229:9229"  # Node.js debugger

    # Restart policy para desarrollo
    restart: unless-stopped

    # Health check más frecuente
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/health"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s

  # ================================================
  # Base de datos con configuración de desarrollo
  # ================================================
  db:
    container_name: cerebro-db-dev
    environment:
      # Configuración más relajada para desarrollo
      - MARIADB_ROOT_PASSWORD=root
    volumes:
      # Datos persistentes en desarrollo
      - db_data_dev:/var/lib/mysql
      # Scripts SQL de desarrollo
      - ./dev-scripts:/docker-entrypoint-initdb.d:ro
    ports:
      - "3306:3306"
    command: >
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_unicode_ci
      --innodb-buffer-pool-size=128M
      --max-connections=100
      --slow-query-log=1
      --long-query-time=1
      --general-log=1
      --general-log-file=/var/lib/mysql/general.log

  # ================================================
  # Redis con configuración de desarrollo
  # ================================================
  redis:
    container_name: cerebro-redis-dev
    volumes:
      # Configuración de desarrollo
      - ./redis-dev.conf:/usr/local/etc/redis/redis.conf:ro
      # Datos persistentes pero no críticos en dev
      - redis_data_dev:/data
    ports:
      - "6379:6379"
    command: redis-server /usr/local/etc/redis/redis.conf

  # ================================================
  # Adminer para gestión de base de datos
  # ================================================
  adminer:
    image: adminer:latest
    container_name: cerebro-adminer-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      - ADMINER_DEFAULT_SERVER=db
      - ADMINER_DESIGN=hydra
    depends_on:
      - db

  # ================================================
  # Redis Commander para gestión de Redis
  # ================================================
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: cerebro-redis-commander-dev
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin
    depends_on:
      - redis

  # ================================================
  # MailHog para testing de emails (futuro)
  # ================================================
  mailhog:
    image: mailhog/mailhog:latest
    container_name: cerebro-mailhog-dev
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    environment:
      - MH_STORAGE=maildir
      - MH_MAILDIR_PATH=/maildir
    volumes:
      - mailhog_data:/maildir

# ================================================
# Volumes para desarrollo
# ================================================
volumes:
  db_data_dev:
    driver: local
  redis_data_dev:
    driver: local
  app_node_modules:
    driver: local
  ts_cache:
    driver: local
  mailhog_data:
    driver: local

# ================================================
# Network para desarrollo
# ================================================
networks:
  default:
    name: cerebro-dev
    driver: bridge
