// scripts/health-check.ts
import { pool } from '../src/db.js';
import { logger } from '../src/logger.js';
import { env } from '../src/env.js';

interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'warning';
  services: {
    database: ServiceStatus;
    redis: ServiceStatus;
    oidc: ServiceStatus;
    jwk: ServiceStatus;
  };
  summary: {
    healthy: number;
    unhealthy: number;
    warnings: number;
  };
}

interface ServiceStatus {
  status: 'healthy' | 'unhealthy' | 'warning';
  message: string;
  details?: any;
  responseTime?: number;
}

// Tipado para respuestas de APIs
interface OIDCConfig {
  issuer?: string;
  authorization_endpoint?: string;
  token_endpoint?: string;
  jwks_uri?: string;
  [key: string]: any;
}

interface JWKSResponse {
  keys?: Array<{
    kid?: string;
    kty?: string;
    use?: string;
    [key: string]: any;
  }>;
}

// Utility function para crear timeout signal compatible
function createTimeoutSignal(ms: number): AbortSignal {
  if (typeof AbortSignal.timeout === 'function') {
    return AbortSignal.timeout(ms);
  }

  // Fallback para Node < 16
  const controller = new AbortController();
  setTimeout(() => controller.abort(), ms);
  return controller.signal;
}

async function checkDatabase(): Promise<ServiceStatus> {
  const start = Date.now();

  try {
    // Test conexión básica
    await pool.query('SELECT 1');

    // Verificar tablas principales
    const [tables]: any[] = await pool.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      AND table_name IN ('users', 'roles', 'oidc_clients', 'jwk_keys', 'user_roles')
    `);

    const expectedTables = ['users', 'roles', 'oidc_clients', 'jwk_keys', 'user_roles'];
    const foundTables = (tables as any[]).map((t: any) => t.table_name);
    const missingTables = expectedTables.filter(table => !foundTables.includes(table));

    if (missingTables.length > 0) {
      return {
        status: 'warning',
        message: `Database connected but missing tables: ${missingTables.join(', ')}`,
        details: { foundTables, missingTables },
        responseTime: Date.now() - start
      };
    }

    // Verificar datos básicos
    const [[userCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM users');
    const [[roleCount]]: any[] = await pool.query('SELECT COUNT(*) as count FROM roles');

    return {
      status: 'healthy',
      message: 'Database is healthy',
      details: {
        users: userCount.count,
        roles: roleCount.count,
        tables: foundTables.length
      },
      responseTime: Date.now() - start
    };

  } catch (error: any) {
    return {
      status: 'unhealthy',
      message: `Database connection failed: ${error?.message || 'Unknown error'}`,
      responseTime: Date.now() - start
    };
  }
}

async function checkRedis(): Promise<ServiceStatus> {
  const start = Date.now();

  try {
    // Lazy import de ioredis solo si es necesario
    const { default: Redis } = await import('ioredis');
    const redis = new Redis(env.REDIS_URL);

    // Test ping
    const pong = await redis.ping();

    // Test write/read
    const testKey = `health-check:${Date.now()}`;
    await redis.set(testKey, 'ok', 'EX', 10); // expira en 10 segundos
    const value = await redis.get(testKey);
    await redis.del(testKey);

    await redis.disconnect();

    if (pong !== 'PONG' || value !== 'ok') {
      return {
        status: 'warning',
        message: 'Redis connection unstable',
        responseTime: Date.now() - start
      };
    }

    return {
      status: 'healthy',
      message: 'Redis is healthy',
      responseTime: Date.now() - start
    };

  } catch (error: any) {
    return {
      status: 'unhealthy',
      message: `Redis connection failed: ${error?.message || 'Unknown error'}`,
      responseTime: Date.now() - start
    };
  }
}

async function checkOIDC(): Promise<ServiceStatus> {
  const start = Date.now();

  try {
    // Verificar que el servidor responda
    const response = await fetch(`${env.OIDC_ISSUER}/.well-known/openid-configuration`, {
      method: 'GET',
      headers: { 'Accept': 'application/json' },
      // Timeout de 5 segundos
      signal: createTimeoutSignal(5000)
    });

    if (!response.ok) {
      return {
        status: 'unhealthy',
        message: `OIDC discovery endpoint returned ${response.status}`,
        responseTime: Date.now() - start
      };
    }

    const config = await response.json() as OIDCConfig;

    // Verificar campos requeridos
    const requiredFields: (keyof OIDCConfig)[] = ['issuer', 'authorization_endpoint', 'token_endpoint', 'jwks_uri'];
    const missingFields = requiredFields.filter(field => !config[field]);

    if (missingFields.length > 0) {
      return {
        status: 'warning',
        message: `OIDC config missing fields: ${missingFields.join(', ')}`,
        details: { config },
        responseTime: Date.now() - start
      };
    }

    return {
      status: 'healthy',
      message: 'OIDC configuration is healthy',
      details: {
        issuer: config.issuer,
        endpoints: requiredFields.length
      },
      responseTime: Date.now() - start
    };

  } catch (error: any) {
    if (error.name === 'AbortError') {
      return {
        status: 'unhealthy',
        message: 'OIDC health check timed out',
        responseTime: Date.now() - start
      };
    }

    return {
      status: 'unhealthy',
      message: `OIDC health check failed: ${error?.message || 'Unknown error'}`,
      responseTime: Date.now() - start
    };
  }
}

async function checkJWK(): Promise<ServiceStatus> {
  const start = Date.now();

  try {
    // Verificar JWK en base de datos
    const [keys]: any[] = await pool.query('SELECT kid, is_active FROM jwk_keys WHERE is_active = 1');

    if (keys.length === 0) {
      return {
        status: 'unhealthy',
        message: 'No active JWK found in database',
        responseTime: Date.now() - start
      };
    }

    if (keys.length > 1) {
      return {
        status: 'warning',
        message: `Multiple active JWKs found (${keys.length})`,
        details: { activeKeys: keys.length },
        responseTime: Date.now() - start
      };
    }

    // Verificar JWK endpoint
    try {
      const response = await fetch(`${env.OIDC_ISSUER}/jwks.json`, {
        method: 'GET',
        headers: { 'Accept': 'application/json' },
        signal: createTimeoutSignal(5000)
      });

      if (!response.ok) {
        return {
          status: 'warning',
          message: 'JWK exists in DB but endpoint not accessible',
          responseTime: Date.now() - start
        };
      }

      const jwks = await response.json() as JWKSResponse;
      if (!jwks.keys || jwks.keys.length === 0) {
        return {
          status: 'warning',
          message: 'JWK endpoint returns empty keys',
          responseTime: Date.now() - start
        };
      }
    } catch (endpointError: any) {
      return {
        status: 'warning',
        message: `JWK endpoint check failed: ${endpointError?.message || 'Unknown error'}`,
        responseTime: Date.now() - start
      };
    }

    return {
      status: 'healthy',
      message: 'JWK system is healthy',
      details: {
        activeKeyId: keys[0].kid,
        totalActiveKeys: keys.length
      },
      responseTime: Date.now() - start
    };

  } catch (error: any) {
    return {
      status: 'unhealthy',
      message: `JWK health check failed: ${error?.message || 'Unknown error'}`,
      responseTime: Date.now() - start
    };
  }
}

async function runHealthCheck(): Promise<HealthStatus> {
  logger.info('🏥 Running comprehensive health check...');

  const [database, redis, oidc, jwk] = await Promise.all([
    checkDatabase(),
    checkRedis(),
    checkOIDC(),
    checkJWK()
  ]);

  const services = { database, redis, oidc, jwk };

  // Calcular resumen
  const statuses = Object.values(services);
  const summary = {
    healthy: statuses.filter(s => s.status === 'healthy').length,
    unhealthy: statuses.filter(s => s.status === 'unhealthy').length,
    warnings: statuses.filter(s => s.status === 'warning').length
  };

  // Determinar estado general
  let overallStatus: 'healthy' | 'unhealthy' | 'warning';
  if (summary.unhealthy > 0) {
    overallStatus = 'unhealthy';
  } else if (summary.warnings > 0) {
    overallStatus = 'warning';
  } else {
    overallStatus = 'healthy';
  }

  const healthStatus: HealthStatus = {
    status: overallStatus,
    services,
    summary
  };

  return healthStatus;
}

function logHealthResults(health: HealthStatus) {
  const statusEmoji = {
    healthy: '✅',
    warning: '⚠️',
    unhealthy: '❌'
  };

  logger.info({
    status: health.status,
    summary: health.summary
  }, `${statusEmoji[health.status]} Overall Health: ${health.status.toUpperCase()}`);

  // Log individual services
  Object.entries(health.services).forEach(([serviceName, serviceStatus]) => {
    const emoji = statusEmoji[serviceStatus.status];
    const responseTime = serviceStatus.responseTime ? ` (${serviceStatus.responseTime}ms)` : '';

    logger.info({
      service: serviceName,
      status: serviceStatus.status,
      message: serviceStatus.message,
      details: serviceStatus.details,
      responseTime: serviceStatus.responseTime
    }, `${emoji} ${serviceName}: ${serviceStatus.message}${responseTime}`);
  });

  // Recommendations based on health
  const recommendations: string[] = [];

  if (health.services.database.status === 'warning') {
    recommendations.push('🔧 Run database migrations: npm run migrate');
  }
  if (health.services.database.status === 'unhealthy') {
    recommendations.push('🐳 Start database: make docker-up');
  }
  if (health.services.redis.status === 'unhealthy') {
    recommendations.push('🔴 Check Redis connection in docker-compose');
  }
  if (health.services.jwk.status === 'unhealthy') {
    recommendations.push('🔑 Generate JWK: npm run gen:jwk');
  }
  if (health.services.oidc.status === 'unhealthy') {
    recommendations.push('🌐 Check if server is running on correct port');
  }

  if (recommendations.length > 0) {
    logger.info('💡 Recommendations to fix issues:');
    recommendations.forEach(rec => logger.info(`  ${rec}`));
  }
}

async function runContinuousHealthCheck(intervalMs: number = 30000) {
  logger.info(`🔄 Starting continuous health monitoring (every ${intervalMs/1000}s)`);

  const runCheck = async () => {
    try {
      const health = await runHealthCheck();
      logHealthResults(health);

      // Si hay problemas críticos, log adicional
      if (health.status === 'unhealthy') {
        logger.error('💥 System has critical health issues!');
      }

    } catch (error) {
      logger.error({ error }, '❌ Health check failed with error');
    }
  };

  // Ejecutar inmediatamente
  await runCheck();

  // Luego cada intervalo
  const interval = setInterval(runCheck, intervalMs);

  // Graceful shutdown
  process.on('SIGINT', () => {
    logger.info('🛑 Stopping health monitoring...');
    clearInterval(interval);
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    logger.info('🛑 Stopping health monitoring...');
    clearInterval(interval);
    process.exit(0);
  });
}

// CLI interface
const command = process.argv[2];
const intervalArg = process.argv[3];
const interval = intervalArg ? parseInt(intervalArg) || 30000 : 30000;

async function main() {
  try {
    switch (command) {
      case 'continuous':
        await runContinuousHealthCheck(interval);
        break;

      case 'database':
        const dbHealth = await checkDatabase();
        console.log(JSON.stringify(dbHealth, null, 2));
        break;

      case 'redis':
        const redisHealth = await checkRedis();
        console.log(JSON.stringify(redisHealth, null, 2));
        break;

      case 'oidc':
        const oidcHealth = await checkOIDC();
        console.log(JSON.stringify(oidcHealth, null, 2));
        break;

      case 'jwk':
        const jwkHealth = await checkJWK();
        console.log(JSON.stringify(jwkHealth, null, 2));
        break;

      case 'json':
        const health = await runHealthCheck();
        console.log(JSON.stringify(health, null, 2));
        break;

      case 'check':
      default:
        const healthStatus = await runHealthCheck();
        logHealthResults(healthStatus);

        // Exit code basado en salud
        const exitCode = healthStatus.status === 'healthy' ? 0 :
                        healthStatus.status === 'warning' ? 1 : 2;
        process.exit(exitCode);
    }

  } catch (error) {
    logger.error({ error }, 'Health check process failed');
    process.exit(2);
  } finally {
    if (command !== 'continuous') {
      await pool.end();
    }
  }
}

// Help
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🏥 Cerebro IDP Health Check Tool

Usage:
  tsx scripts/health-check.ts [command] [options]

Commands:
  check         Run full health check (default)
  json          Output results in JSON format
  continuous    Run continuous monitoring
  database      Check database only
  redis         Check Redis only
  oidc          Check OIDC endpoints only
  jwk           Check JWK system only

Options:
  --help, -h    Show this help message

Examples:
  tsx scripts/health-check.ts                    # Full health check
  tsx scripts/health-check.ts json               # JSON output
  tsx scripts/health-check.ts continuous 10000   # Monitor every 10 seconds
  tsx scripts/health-check.ts database           # Database check only

Exit Codes:
  0 = Healthy
  1 = Warnings (non-critical issues)
  2 = Unhealthy (critical issues)
  `);
  process.exit(0);
}

main();
