#!/bin/bash
# scripts/deploy.sh - Script de despliegue para Cerebro IDP

set -euo pipefail

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Variables
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(cd "$SCRIPT_DIR/.." && pwd)"
DEPLOY_ENV="${1:-production}"
COMPOSE_FILE="docker-compose.prod.yml"

# Funciones
log_info() { echo -e "${GREEN}[INFO]${NC} $1"; }
log_warn() { echo -e "${YELLOW}[WARN]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }
log_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

check_requirements() {
    log_step "Checking deployment requirements..."

    # Verificar Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is required but not installed"
        exit 1
    fi

    # Verificar Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose is required but not installed"
        exit 1
    fi

    # Verificar archivo .env.prod
    if [[ ! -f "$PROJECT_DIR/.env.prod" ]]; then
        log_error ".env.prod file not found"
        log_info "Create .env.prod with production values"
        exit 1
    fi

    # Verificar variables críticas
    source "$PROJECT_DIR/.env.prod"
    required_vars=(
        "DB_PASSWORD"
        "DB_ROOT_PASSWORD"
        "JWT_SECRET"
        "ADMIN_PWD"
        "PUBLIC_BASE_URL"
        "OIDC_ISSUER"
    )

    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            log_error "Required environment variable $var is not set in .env.prod"
            exit 1
        fi
    done

    log_info "Requirements check passed"
}

backup_database() {
    log_step "Creating database backup..."

    backup_dir="$PROJECT_DIR/backups/pre-deploy"
    mkdir -p "$backup_dir"

    backup_file="$backup_dir/backup_$(date +%Y%m%d_%H%M%S).sql"

    if docker-compose -f "$COMPOSE_FILE" exec -T db mysqldump -u cerebro -p"$DB_PASSWORD" cerebro > "$backup_file" 2>/dev/null; then
        log_info "Database backup created: $backup_file"
    else
        log_warn "Could not create database backup (database may not be running)"
    fi
}

build_application() {
    log_step "Building application..."

    cd "$PROJECT_DIR"

    # Build de la imagen
    docker build -t cerebro-idp:latest --target production .

    log_info "Application built successfully"
}

deploy_application() {
    log_step "Deploying application..."

    cd "$PROJECT_DIR"

    # Pull de imágenes base
    docker-compose -f "$COMPOSE_FILE" pull db redis nginx

    # Deploy con rolling update
    docker-compose -f "$COMPOSE_FILE" --env-file .env.prod up -d --remove-orphans

    log_info "Application deployed"
}

run_migrations() {
    log_step "Running database migrations..."

    # Esperar a que la base de datos esté lista
    max_attempts=30
    attempt=0

    while [[ $attempt -lt $max_attempts ]]; do
        if docker-compose -f "$COMPOSE_FILE" exec -T db mysqladmin -u cerebro -p"$DB_PASSWORD" ping &>/dev/null; then
            break
        fi

        log_info "Waiting for database... ($((attempt + 1))/$max_attempts)"
        sleep 2
        ((attempt++))
    done

    if [[ $attempt -eq $max_attempts ]]; then
        log_error "Database is not responding after $max_attempts attempts"
        exit 1
    fi

    # Ejecutar migraciones
    docker-compose -f "$COMPOSE_FILE" exec -T app npm run migrate

    log_info "Migrations completed"
}

verify_deployment() {
    log_step "Verifying deployment..."

    # Verificar que los servicios estén corriendo
    if ! docker-compose -f "$COMPOSE_FILE" ps | grep -q "Up"; then
        log_error "Some services are not running"
        docker-compose -f "$COMPOSE_FILE" ps
        exit 1
    fi

    # Health check
    max_attempts=10
    attempt=0

    while [[ $attempt -lt $max_attempts ]]; do
        if curl -f http://localhost:4000/health &>/dev/null; then
            log_info "Health check passed"
            break
        fi

        log_info "Waiting for application to be ready... ($((attempt + 1))/$max_attempts)"
        sleep 5
        ((attempt++))
    done

    if [[ $attempt -eq $max_attempts ]]; then
        log_error "Application health check failed"
        exit 1
    fi

    # Verificar endpoints OIDC
    if curl -f http://localhost:4000/.well-known/openid-configuration &>/dev/null; then
        log_info "OIDC endpoints are working"
    else
        log_warn "OIDC endpoints may not be working properly"
    fi

    log_info "Deployment verification completed"
}

rollback_deployment() {
    log_step "Rolling back deployment..."

    # TODO: Implementar rollback con versiones anteriores
    log_warn "Rollback not implemented yet"
    log_info "To rollback manually:"
    log_info "1. docker-compose -f $COMPOSE_FILE down"
    log_info "2. Restore database from backup"
    log_info "3. Deploy previous version"
}

cleanup_old_images() {
    log_step "Cleaning up old images..."

    # Remover imágenes sin usar
    docker image prune -f

    # Remover contenedores parados
    docker container prune -f

    log_info "Cleanup completed"
}

show_status() {
    log_step "Deployment status:"

    echo
    log_info "Services:"
    docker-compose -f "$COMPOSE_FILE" ps

    echo
    log_info "Health endpoints:"
    echo "- Application: http://localhost:4000/health"
    echo "- OIDC Config: http://localhost:4000/.well-known/openid-configuration"
    echo "- JWK: http://localhost:4000/jwks.json"

    echo
    log_info "Logs:"
    echo "- Application: docker-compose -f $COMPOSE_FILE logs app"
    echo "- Database: docker-compose -f $COMPOSE_FILE logs db"
    echo "- All: docker-compose -f $COMPOSE_FILE logs"
}

main() {
    log_info "Starting deployment for environment: $DEPLOY_ENV"

    case "${1:-deploy}" in
        "deploy")
            check_requirements
            backup_database
            build_application
            deploy_application
            run_migrations
            verify_deployment
            cleanup_old_images
            show_status
            log_info "🎉 Deployment completed successfully!"
            ;;
        "rollback")
            rollback_deployment
            ;;
        "status")
            show_status
            ;;
        "backup")
            backup_database
            ;;
        "migrate")
            run_migrations
            ;;
        *)
            echo "Usage: $0 [deploy|rollback|status|backup|migrate]"
            exit 1
            ;;
    esac
}

# Manejo de señales
trap 'log_error "Deployment interrupted"; exit 1' INT TERM

# Ejecutar función principal
main "$@"
