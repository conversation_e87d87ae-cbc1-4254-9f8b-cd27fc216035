{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "<PERSON><PERSON><PERSON>", "outDir": "./dist", "strict": true, "noImplicitOverride": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true, "sourceMap": true, "types": ["node"]}, "include": ["src/**/*.ts", "scripts/**/*.ts"], "exclude": ["node_modules", "dist"]}