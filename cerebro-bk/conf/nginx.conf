# nginx.conf for Cerebro IDP - Production ready

worker_processes auto;
worker_rlimit_nofile 65535;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # ================================================
    # LOGGING
    # ================================================
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # ================================================
    # PERFORMANCE
    # ================================================
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    client_max_body_size 1M;
    client_body_timeout 12;
    client_header_timeout 12;
    send_timeout 10;

    # ================================================
    # GZIP COMPRESSION
    # ================================================
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # ================================================
    # RATE LIMITING
    # ================================================
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
    limit_req_zone $binary_remote_addr zone=admin:10m rate=2r/s;

    # ================================================
    # UPSTREAM DEFINITION
    # ================================================
    upstream cerebro_backend {
        server app:4000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # ================================================
    # HTTP TO HTTPS REDIRECT
    # ================================================
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }

    # ================================================
    # MAIN SERVER BLOCK
    # ================================================
    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        # ================================================
        # SSL CONFIGURATION
        # ================================================
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:50m;
        ssl_session_tickets off;

        # Modern configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # OCSP stapling
        ssl_stapling on;
        ssl_stapling_verify on;

        # ================================================
        # SECURITY HEADERS
        # ================================================
        add_header Strict-Transport-Security "max-age=63072000" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # ================================================
        # PROXY SETTINGS
        # ================================================
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;

        # ================================================
        # ROUTES
        # ================================================

        # Health check (no rate limit)
        location /health {
            proxy_pass http://cerebro_backend;
            access_log off;
        }

        # OIDC endpoints (moderate rate limiting)
        location ~ ^/(\.well-known|jwks\.json|userinfo) {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://cerebro_backend;

            # Cache OIDC configuration
            location /.well-known/openid-configuration {
                proxy_pass http://cerebro_backend;
                proxy_cache_valid 200 1h;
                add_header Cache-Control "public, max-age=3600";
            }

            # Cache JWK
            location /jwks.json {
                proxy_pass http://cerebro_backend;
                proxy_cache_valid 200 1h;
                add_header Cache-Control "public, max-age=3600";
            }
        }

        # Authentication endpoints (strict rate limiting)
        location ~ ^/(authorize|token) {
            limit_req zone=auth burst=10 nodelay;
            proxy_pass http://cerebro_backend;
        }

        # Admin endpoints (very strict rate limiting)
        location /admin {
            limit_req zone=admin burst=5 nodelay;
            proxy_pass http://cerebro_backend;

            # Additional security for admin
            add_header X-Admin-Access "restricted" always;
        }

        # All other API endpoints
        location / {
            limit_req zone=api burst=30 nodelay;
            proxy_pass http://cerebro_backend;
        }

        # ================================================
        # ERROR PAGES
        # ================================================
        error_page 429 @rate_limit;
        error_page 502 503 504 @backend_error;

        location @rate_limit {
            return 429 '{"error":"rate_limit_exceeded","message":"Too many requests"}';
            add_header Content-Type application/json always;
        }

        location @backend_error {
            return 503 '{"error":"service_unavailable","message":"Backend temporarily unavailable"}';
            add_header Content-Type application/json always;
        }
    }

    # ================================================
    # MONITORING ENDPOINT
    # ================================================
    server {
        listen 8080;
        server_name localhost;
        location /nginx_status {
            stub_status on;
            access_log off;
            allow 127.0.0.1;
            allow **********/16;  # Docker network
            deny all;
        }
    }
}
