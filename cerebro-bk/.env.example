# ================================================
# 🧠 CEREBRO IDENTITY PROVIDER - CONFIGURACIÓN
# ================================================

# Entorno
NODE_ENV=development
LOG_LEVEL=info

# ================================================
# 🌐 SERVIDOR
# ================================================
PORT=4000
PUBLIC_BASE_URL=http://localhost:4000

# ================================================
# 🗄️ BASE DE DATOS (MariaDB/MySQL)
# ================================================
DB_HOST=127.0.0.1
DB_PORT=3306
DB_USER=cerebro
DB_PASSWORD=cerebro
DB_NAME=cerebro

# ================================================
# 🔴 REDIS (Caché y Sessions)
# ================================================
REDIS_URL=redis://127.0.0.1:6379

# ================================================
# 🔐 OIDC & JWT
# ================================================
# URL del issuer OIDC (debe coincidir con PUBLIC_BASE_URL)
OIDC_ISSUER=http://localhost:4000

# Audiencia de los access tokens
OIDC_AUDIENCE=cerebro

# Secreto para JWT (¡CAMBIAR EN PRODUCCIÓN!)
JWT_SECRET=dev-secret-change-me-in-production-very-important

# TTL de tokens en segundos
ACCESS_TOKEN_TTL=3600      # 1 hora
REFRESH_TOKEN_TTL=2592000  # 30 días

# Rotación de claves JWK (se llena automáticamente con gen:jwk)
JWK_ACTIVE_KID=

# ================================================
# 👤 USUARIO ADMIN INICIAL
# ================================================
ADMIN_PWD=SuperSegura123!

# ================================================
# 📊 AUDIT & LIMPIEZA
# ================================================
# Retención de audit logs en días
AUDIT_LOG_RETENTION_DAYS=90

# ================================================
# 🔒 SEGURIDAD
# ================================================
# Configuración de rate limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=120

# Configuración de CORS (separado por comas en prod)
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080

# ================================================
# 📧 EMAIL (Futuro)
# ================================================
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM=<EMAIL>

# ================================================
# 📱 2FA / TOTP (Futuro)
# ================================================
# TOTP_ISSUER_NAME=Cerebro IDP
# TOTP_WINDOW=1

# ================================================
# 💳 STRIPE (Futuro)
# ================================================
# STRIPE_PUBLIC_KEY=pk_test_...
# STRIPE_SECRET_KEY=sk_test_...
# STRIPE_WEBHOOK_SECRET=whsec_...

# ================================================
# 📊 MONITOREO (Futuro)
# ================================================
# SENTRY_DSN=https://...
# PROMETHEUS_METRICS=true
# PROMETHEUS_PORT=9090

# ================================================
# 🚀 DEPLOYMENT
# ================================================
# En producción, usar URLs reales:
# PUBLIC_BASE_URL=https://auth.tu-dominio.com
# OIDC_ISSUER=https://auth.tu-dominio.com
# CORS_ORIGINS=https://app.tu-dominio.com,https://admin.tu-dominio.com

# SSL/TLS en producción
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

# ================================================
# 🐳 DOCKER OVERRIDES (Opcional)
# ================================================
# DB_CONTAINER_NAME=cerebro-db
# REDIS_CONTAINER_NAME=cerebro-redis

# ================================================
# 🔧 DESARROLLO Y DEBUG
# ================================================
# DEBUG=cerebro:*
# ENABLE_QUERY_LOGGING=false
# DISABLE_AUTH_FOR_HEALTH=false
